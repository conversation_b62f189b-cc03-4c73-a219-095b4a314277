#!/usr/bin/env node

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

async function testCostCalculation() {
    try {
        // Import the pricing constants
        const { GEMINI_PRICES } = await import("../packages/shared/constants/rate-limits.js");
        
        console.log("🧮 Testing cost calculation...");
        console.log("\nCurrent pricing (USD per request):");
        
        Object.entries(GEMINI_PRICES).forEach(([model, price]) => {
            const costCents = Math.round(price * 100);
            console.log(`  ${model}: $${price} USD = ${costCents} cents`);
        });
        
        console.log("\nTesting with 30 requests:");
        Object.entries(GEMINI_PRICES).forEach(([model, price]) => {
            const costCents = Math.round(price * 100);
            const totalCostCents = costCents * 30;
            const totalCostUsd = (totalCostCents / 100).toFixed(2);
            console.log(`  ${model}: 30 requests = ${totalCostCents} cents = $${totalCostUsd} USD`);
        });
        
    } catch (error) {
        console.error("❌ Error:", error.message);
    }
}

testCostCalculation().then(() => {
    console.log("\n✅ Cost calculation test completed");
    process.exit(0);
});
