#!/usr/bin/env node

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

async function testAdminCostFix() {
    try {
        console.log("🧮 Testing admin cost calculation fix...");
        
        // Test the admin API endpoint
        const response = await fetch("http://localhost:3002/api/admin/system-metrics");
        
        if (!response.ok) {
            console.error(`❌ API request failed: ${response.status} ${response.statusText}`);
            return;
        }
        
        const data = await response.json();
        
        console.log("\n📊 Provider Usage Stats:");
        if (data.providerStats && data.providerStats.length > 0) {
            data.providerStats.forEach(stat => {
                console.log(`  ${stat.provider}:`);
                console.log(`    Requests: ${stat.requests}`);
                console.log(`    Cost: $${stat.costUsd} USD`);
                console.log(`    Unique Users: ${stat.uniqueUsers}`);
                console.log(`    Avg Cost/Request: $${stat.avgCostPerRequest} USD`);
                console.log("");
            });
        } else {
            console.log("  No provider usage data found");
        }
        
        // Check if the cost is now non-zero
        const geminiStats = data.providerStats?.find(stat => stat.provider === 'gemini');
        if (geminiStats) {
            const cost = parseFloat(geminiStats.costUsd);
            if (cost > 0) {
                console.log("✅ Cost calculation fix successful! Gemini shows non-zero cost.");
            } else {
                console.log("❌ Cost calculation still shows $0.00 - may need more test data or higher pricing.");
            }
        } else {
            console.log("ℹ️  No Gemini usage data found - this is expected if no requests have been made yet.");
        }
        
    } catch (error) {
        console.error("❌ Error:", error.message);
    }
}

testAdminCostFix().then(() => {
    console.log("\n✅ Admin cost fix test completed");
    process.exit(0);
});
