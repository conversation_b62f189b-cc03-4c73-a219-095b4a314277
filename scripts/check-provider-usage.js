#!/usr/bin/env node

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

async function checkProviderUsage() {
    try {
        // Dynamic imports to ensure env vars are loaded first
        const { db } = await import("../apps/web/lib/database/index.js");
        const { providerUsage } = await import("../apps/web/lib/database/schema.js");
        const { count, sum, sql, gte } = await import("drizzle-orm");

        console.log("🔍 Checking provider usage data...");

        // Check total records
        const [totalRecords] = await db.select({ count: count() }).from(providerUsage);

        console.log(`📊 Total provider usage records: ${totalRecords.count}`);

        if (totalRecords.count === 0) {
            console.log("❌ No provider usage records found!");
            return;
        }

        // Check records by provider
        const providerBreakdown = await db
            .select({
                provider: providerUsage.provider,
                totalRequests: count(),
                totalCostCents: sum(providerUsage.estimatedCostCents),
            })
            .from(providerUsage)
            .groupBy(providerUsage.provider);

        console.log("\n📈 Provider breakdown:");
        providerBreakdown.forEach((stat) => {
            const costUsd = stat.totalCostCents
                ? (Number(stat.totalCostCents) / 100).toFixed(2)
                : "0.00";
            console.log(`  ${stat.provider}: ${stat.totalRequests} requests, $${costUsd} USD`);
        });

        // Check last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const last30DaysStats = await db
            .select({
                provider: providerUsage.provider,
                totalRequests: count(),
                totalCostCents: sum(providerUsage.estimatedCostCents),
            })
            .from(providerUsage)
            .where(gte(providerUsage.requestTimestamp, thirtyDaysAgo))
            .groupBy(providerUsage.provider);

        console.log("\n📅 Last 30 days breakdown:");
        last30DaysStats.forEach((stat) => {
            const costUsd = stat.totalCostCents
                ? (Number(stat.totalCostCents) / 100).toFixed(2)
                : "0.00";
            console.log(`  ${stat.provider}: ${stat.totalRequests} requests, $${costUsd} USD`);
        });

        // Check sample records
        const sampleRecords = await db.select().from(providerUsage).limit(5);

        console.log("\n🔍 Sample records:");
        sampleRecords.forEach((record) => {
            const costUsd = (record.estimatedCostCents / 100).toFixed(4);
            console.log(
                `  ${record.provider} - ${record.modelId}: $${costUsd} USD (${record.estimatedCostCents} cents)`,
            );
        });
    } catch (error) {
        console.error("❌ Error:", error.message);
    }
}

checkProviderUsage().then(() => {
    console.log("\n✅ Provider usage check completed");
    process.exit(0);
});
