/**
 * Global rate-limit numbers used in UI copy and backend logic.
 * Single source of truth for all Gemini model rate limiting.
 *
 * NOTE: Only VT+ users get server-funded access. FREE limits are unused
 * for server-funded access (free users must use BYOK).
 *
 * Updated limits based on current VT+ offering and usage patterns.
 */
export const GEMINI_LIMITS = {
    FLASH_LITE: { FREE_DAY: 20, PLUS_DAY: 100, FREE_MINUTE: 5, PLUS_MINUTE: 10 },
    FLASH: { FREE_DAY: 10, PLUS_DAY: 50, FREE_MINUTE: 3, PLUS_MINUTE: 8 },
    PRO: { FREE_DAY: 5, PLUS_DAY: 25, FREE_MINUTE: 2, PLUS_MINUTE: 5 },
    // Gemini 2.0 models use same limits as their 2.5 counterparts
    FLASH_2_0: { FREE_DAY: 10, PLUS_DAY: 50, FREE_MINUTE: 3, PLUS_MINUTE: 8 },
    FLASH_LITE_2_0: { FREE_DAY: 20, PLUS_DAY: 100, FREE_MINUTE: 5, PLUS_MINUTE: 10 },
} as const;

/**
 * Estimated cost per request in USD (rough flat estimate)
 * Used for budget tracking and cost projections
 *
 * Based on Gemini API pricing as of Jan 2025:
 * - Flash-Lite: $0.1/1M input + $0.4/1M output tokens
 * - Flash: $0.3/1M input + $2.5/1M output tokens
 * - Pro: $1.25/1M input + $10/1M output tokens
 *
 * Estimates assume ~1000 input + 500 output tokens per request
 */
export const GEMINI_PRICES = {
    FLASH_LITE: 0.005, // ~$0.1*1k/1M + $0.4*0.5k/1M = $0.0003, rounded up to $0.005 (0.5 cents) to ensure non-zero tracking
    FLASH: 0.01, // ~$0.3*1k/1M + $2.5*0.5k/1M = $0.00155, rounded up to $0.01 (1 cent)
    PRO: 0.02, // ~$1.25*1k/1M + $10*0.5k/1M = $0.00625, rounded up to $0.02 (2 cents)
    "gemini-2.5-flash-lite-preview-06-17": 0.005,
} as const;

/**
 * Budget constraints for Gemini API usage
 */
export const BUDGET_LIMITS = {
    MONTHLY_CAP_USD: 80, // $80/month = 80% of $300/3-month budget
    WARNING_THRESHOLD: 0.8, // 80% of monthly cap
} as const;

/**
 * @deprecated Use GEMINI_LIMITS.FLASH_LITE instead
 */
export const GEMINI_FLASH_LIMITS = GEMINI_LIMITS.FLASH_LITE;

/**
 * Helper functions for consistent UI copy formatting.
 * Keeps UI components DRY and ensures consistent messaging.
 */
export const limitText = {
    free: (model: keyof typeof GEMINI_LIMITS = "FLASH_LITE") =>
        `${GEMINI_LIMITS[model].FREE_DAY} requests/day, ${GEMINI_LIMITS[model].FREE_MINUTE}/min`,
    plus: (model: keyof typeof GEMINI_LIMITS = "FLASH_LITE") =>
        `${GEMINI_LIMITS[model].PLUS_DAY} requests/day, ${GEMINI_LIMITS[model].PLUS_MINUTE}/min`,
    compare: (model: keyof typeof GEMINI_LIMITS = "FLASH_LITE") =>
        `${limitText.plus(model)} vs ${GEMINI_LIMITS[model].FREE_DAY}/day, ${GEMINI_LIMITS[model].FREE_MINUTE}/min`,
} as const;
